{"id": "5ccf4570-3646-4ea3-96b9-1281448e5993", "rows_written": 102, "rows_read": 335, "storage_bytes_used": 86016, "write_requests_delegated": 0, "current_frame_no": 41, "top_query_threshold": 1, "top_queries": [{"rows_written": 0, "rows_read": 1, "query": "CREATE TABLE IF NOT EXISTS migrations(name TEXT PRIMARY KEY, hash TEXT)"}, {"rows_written": 0, "rows_read": 1, "query": "SELECT * FROM \"catalog\" WHERE \"id\" = ?;"}, {"rows_written": 0, "rows_read": 1, "query": "SELECT * FROM catalog WHERE id = ? AND nav IS NULL LIMIT 1;"}, {"rows_written": 0, "rows_read": 1, "query": "SELECT * FROM products WHERE category = ? AND enabled;"}, {"rows_written": 0, "rows_read": 1, "query": "SELECT * FROM users WHERE id = ?;"}, {"rows_written": 0, "rows_read": 1, "query": "SELECT hash FROM migrations WHERE name = ?;"}, {"rows_written": 0, "rows_read": 1, "query": "SELECT value FROM attributes WHERE KEY = ?;"}, {"rows_written": 1, "rows_read": 1, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;"}, {"rows_written": 1, "rows_read": 1, "query": "UPDATE catalog SET media = ? WHERE \"id\" = ?;"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;"}], "slowest_query_threshold": 2, "slowest_queries": [{"elapsed_ms": 2, "query": "INSERT INTO catalog (id, kind, media, description, nav) VALUES ('root', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"🗂 Канал-каталог анкет\",\n            \"url\": \"https://example.com\"\n        },\n        {\n            \"btn\": \"🔐 Пак кружков (обезличенные)\",\n            \"product_category\": \"pack\"\n        },\n        {\n            \"btn\": \"🧚🏻‍♀️ Модели классика\",\n            \"dir\": \"classic\"\n        },\n        {\n            \"btn\": \"🔞 Модели onlyfans\",\n            \"dir\": \"onlyfans\"\n        },\n        {\n            \"btn\": \"🧖🏻‍♀️ Модели домашние\",\n            \"dir\": \"home\"\n        }\n    ]'), ('classic', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"classic-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"classic-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"classic-big\"\n        }\n    ]'), ('onlyfans', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"onlyfans-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"onlyfans-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"onlyfans-big\"\n        }\n    ]'), ('home', 'directory', 'accounts', NULL, '[\n        {\n            \"btn\": \"малый пак\",\n            \"product_category\": \"home-small\"\n        },\n        {\n            \"btn\": \"средний пак\",\n            \"product_category\": \"home-medium\"\n        },\n        {\n            \"btn\": \"большой пак\",\n            \"product_category\": \"home-big\"\n        }\n    ]');", "rows_written": 8, "rows_read": 0}, {"elapsed_ms": 2, "query": "SELECT * FROM `catalog` ORDER BY `id` ASC LIMIT 100 OFFSET 0;", "rows_written": 0, "rows_read": 14}, {"elapsed_ms": 2, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;", "rows_written": 0, "rows_read": 14}, {"elapsed_ms": 2, "query": "SELECT * FROM users WHERE id = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 2, "query": "SELECT hash FROM migrations WHERE name = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 2, "query": "SELECT name FROM sqlite_master WHERE type = 'table' ORDER BY name;", "rows_written": 0, "rows_read": 32}, {"elapsed_ms": 3, "query": "UPDATE catalog SET media = ? WHERE \"id\" = ?;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 4, "query": "COMMIT;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 4, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 7, "query": "CREATE TABLE IF NOT EXISTS migrations(name TEXT PRIMARY KEY, hash TEXT)", "rows_written": 3, "rows_read": 1}], "embedded_replica_frames_replicated": 0, "query_count": 158, "query_latency": 108628}