use color_eyre::{
    eyre::{eyre, Context as _},
    Report,
};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, fs, path::Path, sync::Arc};
use teloxide::types::InputFile;
use tokio::sync::{OnceCell, RwLock};
use tracing::instrument;

use crate::context::Context;

/// Global media cache that stores media files in memory
pub type MediaCache = Arc<RwLock<HashMap<String, Vec<u8>>>>;

/// Global media cache instance
static MEDIA_CACHE: OnceCell<MediaCache> = OnceCell::const_new();

/// Get the global media cache instance
pub fn get_global_media_cache() -> &'static MediaCache {
    MEDIA_CACHE.get().expect("Media cache not initialized")
}

/// Initialize the global media cache
pub async fn initialize_global_media_cache() -> &'static MediaCache {
    MEDIA_CACHE
        .get_or_init(|| async { Arc::new(RwLock::new(HashMap::new())) })
        .await
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq, Hash)]
pub enum Media {
    Photo(String),
    Video(String),
}

impl<'de> Deserialize<'de> for Media {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        let (name, ext) = s.split_once('.').ok_or_else(|| {
            serde::de::Error::custom(format!(
                "Failed to deserialize media: {s}, should be name.ext"
            ))
        })?;
        match ext {
            "jpg" | "jpeg" | "png" => Ok(Media::Photo(name.to_owned())),
            "mp4" => Ok(Media::Video(name.to_owned())),
            _ => Err(serde::de::Error::custom(format!(
                "Failed to deserialize media: {s}, unknown ext {ext}"
            ))),
        }
    }
}

impl Serialize for Media {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        match self {
            Media::Photo(name) => serializer.serialize_str(&format!("{name}.jpg")),
            Media::Video(name) => serializer.serialize_str(&format!("{name}.mp4")),
        }
    }
}

impl Media {
    pub fn video(name: impl Into<String>) -> Self {
        Self::Video(name.into())
    }

    pub fn photo(name: impl Into<String>) -> Self {
        Self::Photo(name.into())
    }

    pub fn file_name(&self) -> String {
        match self {
            Media::Photo(name) => format!("{name}.jpg"),
            Media::Video(name) => format!("{name}.mp4"),
        }
    }

    pub fn name(&self) -> &str {
        match self {
            Media::Photo(name) => name,
            Media::Video(name) => name,
        }
    }

    // pub fn group(names: impl Into<Vec<String>>) -> Self {
    //     Self::Group(names.into())
    // }
}

// media files are stored in memory cache

impl Context {
    #[instrument(skip_all, fields(media_name = %media.name(), bot = %self.bot_username))]
    pub async fn save_media_id_from_message(
        &self,
        media: &Media,
        message: &teloxide::types::Message,
    ) -> Result<(), Report> {
        tracing::debug!("Saving media ID from message for media: {}", media.name());

        // Extract file ID from the message based on media type
        let file_id = match media {
            Media::Photo(_) => {
                if let Some(photo_sizes) = message.photo() {
                    if let Some(largest_photo) = photo_sizes.last() {
                        Some(largest_photo.file.id.clone())
                    } else {
                        tracing::warn!("No photo sizes found in message");
                        None
                    }
                } else {
                    tracing::warn!("Expected photo but message contains no photo");
                    None
                }
            }
            Media::Video(_) => {
                if let Some(video) = message.video() {
                    Some(video.file.id.clone())
                } else {
                    tracing::warn!("Expected video but message contains no video");
                    None
                }
            }
        };

        if let Some(file_id) = file_id {
            tracing::info!(
                "Saving file ID '{}' for media '{}' with bot '{}'",
                file_id,
                media.name(),
                self.bot_username
            );

            self.db
                .set_file_id(&self.bot_username, media.name(), &file_id)
                .await
                .wrap_err("Failed to save media file ID to database")?;
        } else {
            tracing::error!(
                "Could not extract file ID from message for media: {}",
                media.name()
            );
        }

        Ok(())
    }

    pub async fn get_media_memory(&self, media: &Media) -> Result<InputFile, Report> {
        // Get media from memory cache
        let media_cache = get_global_media_cache();
        let cache = media_cache.read().await;

        let stored_media = cache
            .get(media.name())
            .ok_or_else(|| eyre!("No media data found for '{}' in cache", media.name()))?;

        let media_name = media.file_name();

        Ok(InputFile::memory(stored_media.clone()).file_name(media_name))
    }

    #[instrument(skip_all, fields(media_name = %media.name(), bot = %self.bot_username))]
    pub async fn get_media_file_id(&self, media: &Media) -> Result<Option<InputFile>, Report> {
        tracing::debug!("Getting cached file ID for media: {}", media.name());

        let file_id = self
            .db
            .get_file_id(&self.bot_username, media.name())
            .await
            .wrap_err("Failed to get media file ID from database")?;

        if let Some(file_id) = file_id {
            tracing::debug!(
                "Found cached file ID '{}' for media '{}' with bot '{}'",
                file_id,
                media.name(),
                self.bot_username
            );
            Ok(Some(InputFile::file_id(file_id)))
        } else {
            tracing::debug!(
                "No cached file ID found for media '{}' with bot '{}'",
                media.name(),
                self.bot_username
            );
            Ok(None)
        }
    }
}

/// Initialize media files from the assets folder into memory cache
/// Only loads files that don't already exist in the cache with similar names
#[instrument]
pub async fn initialize_media_from_assets() -> Result<(), Report> {
    tracing::info!("Initializing media from assets folder into memory cache");

    let assets_path = Path::new("assets");
    if !assets_path.exists() {
        tracing::warn!("Assets folder does not exist, skipping media initialization");
        return Ok(());
    }

    // Initialize global media cache
    let media_cache = initialize_global_media_cache().await;
    let mut cache = media_cache.write().await;

    tracing::debug!("Found {} existing media files in cache", cache.len());

    // Read all files from assets directory
    let entries = fs::read_dir(assets_path).wrap_err("Failed to read assets directory")?;

    let mut loaded_count = 0;
    let mut skipped_count = 0;

    for entry in entries {
        let entry = entry.wrap_err("Failed to read directory entry")?;
        let path = entry.path();

        if !path.is_file() {
            continue;
        }

        let Some(file_name) = path.file_name().and_then(|n| n.to_str()) else {
            tracing::warn!("Skipping file with invalid name: {:?}", path);
            continue;
        };

        // Check if this is a supported media file
        let media = match file_name.split('.').collect::<Vec<_>>().as_slice() {
            [name, "mp4"] => Media::Video(name.to_string()),
            [name, "jpg"] | [name, "jpeg"] | [name, "png"] => Media::Photo(name.to_string()),
            _ => {
                tracing::debug!("Skipping unsupported file: {}", file_name);
                continue;
            }
        };

        // Check if media with this name already exists in cache
        if cache.contains_key(media.name()) {
            tracing::debug!("Media '{}' already exists in cache, skipping", media.name());
            skipped_count += 1;
            continue;
        }

        // Load file data
        let file_data =
            fs::read(&path).wrap_err_with(|| format!("Failed to read file: {:?}", path))?;

        // Save to cache
        cache.insert(media.name().to_string(), file_data.clone());

        tracing::info!(
            "Loaded media '{}' from assets into cache ({} bytes)",
            media.name(),
            file_data.len()
        );
        loaded_count += 1;
    }

    tracing::info!(
        "Media initialization complete: {} files loaded, {} files skipped, {} total in cache",
        loaded_count,
        skipped_count,
        cache.len()
    );

    Ok(())
}

/// Save media to both memory cache and file system
/// This ensures the media persists across application restarts
#[instrument(skip(data))]
pub async fn save_media_to_cache_and_file(media: &Media, data: &[u8]) -> Result<(), Report> {
    // Save to memory cache
    {
        let media_cache = get_global_media_cache();
        let mut cache = media_cache.write().await;
        cache.insert(media.name().to_string(), data.to_vec());
        tracing::debug!("Saved media '{}' to cache", media.name());
    }

    // Save to file system
    let assets_path = Path::new("assets");
    if !assets_path.exists() {
        fs::create_dir_all(assets_path).wrap_err("Failed to create assets directory")?;
        tracing::info!("Created assets directory");
    }

    let file_path = assets_path.join(media.file_name());
    fs::write(&file_path, data)
        .wrap_err_with(|| format!("Failed to save media file {:?}", file_path))?;

    tracing::info!(
        "Saved media '{}' to file system at {:?} ({} bytes)",
        media.name(),
        file_path,
        data.len()
    );

    Ok(())
}

/// Check if media exists in the cache
pub async fn media_exists_in_cache(media_name: &str) -> bool {
    let media_cache = get_global_media_cache();
    let cache = media_cache.read().await;
    cache.contains_key(media_name)
}

/// Get all media names currently in cache
pub async fn get_cached_media_names() -> Vec<String> {
    let media_cache = get_global_media_cache();
    let cache = media_cache.read().await;
    cache.keys().cloned().collect()
}
